#!/usr/bin/env python3
"""
Test script for the new /v2/apply_subtitle_style API endpoint
"""

import requests
import json
import time

# API configuration
API_BASE_URL = "http://127.0.0.1:9011"

def test_apply_subtitle_style():
    """Test the new subtitle styling and dubbing endpoint"""
    
    # Test data
    test_data = {
        "srt_url": "https://example.com/test.srt",  # Replace with actual SRT URL
        "video_url": "https://example.com/test.mp4",  # Replace with actual video URL
        "subtitle_settings": {
            "fontname": "Arial",
            "fontsize": 20,
            "fontcolor": "&hffffff",  # White
            "fontbordercolor": "&h000000",  # Black
            "subtitle_position": 2,  # Bottom
            "subtitle_type": 1  # Hard subtitles
        },
        "dubbing_settings": {
            "tts_type": 0,  # Edge-TTS
            "voice_role": "zh-CN-YunjianNeural",
            "voice_rate": "+0%",
            "volume": "+0%",
            "pitch": "+0Hz",
            "target_language": "zh-cn"
        }
    }
    
    print("Testing /v2/apply_subtitle_style endpoint...")
    print(f"Request data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Send request
        response = requests.post(
            f"{API_BASE_URL}/v2/apply_subtitle_style",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get('code') == 0:
                task_id = result.get('task_id')
                print(f"Task created successfully with ID: {task_id}")
                
                # Monitor task progress
                monitor_task_progress(task_id)
            else:
                print(f"Error: {result.get('msg')}")
        else:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

def monitor_task_progress(task_id):
    """Monitor the progress of a task"""
    print(f"\nMonitoring task progress for ID: {task_id}")
    
    max_attempts = 60  # Monitor for up to 5 minutes
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.post(
                f"{API_BASE_URL}/task_status",
                json={"task_id": task_id},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code')
                msg = result.get('msg', '')
                
                if code == -1:
                    # In progress
                    print(f"Progress: {msg}")
                elif code == 0:
                    # Success
                    print(f"Task completed successfully!")
                    data = result.get('data', {})
                    urls = data.get('url', [])
                    print(f"Generated files: {urls}")
                    break
                else:
                    # Error
                    print(f"Task failed: {msg}")
                    break
            else:
                print(f"Failed to get task status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"Failed to check task status: {e}")
        
        attempt += 1
        time.sleep(5)  # Wait 5 seconds before next check
    
    if attempt >= max_attempts:
        print("Task monitoring timed out")

def test_voice_list():
    """Test the voice list endpoint"""
    print("\nTesting /voices endpoint...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/voices",
            params={"tts_type": 0, "language": "zh"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"Voice list response: {json.dumps(result, indent=2)}")
        else:
            print(f"Failed to get voice list: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Voice list request failed: {e}")

def test_basic_endpoints():
    """Test basic API endpoints to ensure server is running"""
    print("Testing basic endpoints...")
    
    # Test a simple endpoint
    try:
        response = requests.get(f"{API_BASE_URL}/voices?tts_type=0", timeout=5)
        if response.status_code == 200:
            print("✓ API server is running")
            return True
        else:
            print(f"✗ API server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to API server: {e}")
        return False

if __name__ == "__main__":
    print("=== PyVideoTrans API Test ===")
    
    # Test basic connectivity first
    if not test_basic_endpoints():
        print("Please ensure the API server is running on http://127.0.0.1:9011")
        exit(1)
    
    # Test voice list endpoint
    test_voice_list()
    
    # Test the new subtitle styling endpoint
    # Note: This will fail with the example URLs, but will test the endpoint structure
    test_apply_subtitle_style()
    
    print("\n=== Test completed ===")
