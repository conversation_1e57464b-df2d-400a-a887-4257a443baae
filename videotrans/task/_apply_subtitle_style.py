import os
import shutil
from pathlib import Path
from typing import Dict

from videotrans.configure import config
from videotrans.task._base import BaseTask
from videotrans.task._dubbing import DubbingSrt
from videotrans.util import tools


class ApplySubtitleStyleTask(BaseTask):
    """
    Apply subtitle styling and dubbing to video task
    
    cfg={
        'srt_path': str,  # Local path to SRT file
        'video_path': str,  # Local path to video file
        'voice_separation': bool,
        'source_language': str,
        'target_language': str,
        'subtitle_layout': str,  # 'single' or 'double'
        'subtitle_style': dict,  # Primary and secondary styling
        'dubbing_settings': dict,  # Optional dubbing configuration
        'target_dir': str,
        'cache_folder': str,
        'uuid': str,
        'basename': str,
        'noextname': str
    }
    """

    def __init__(self, cfg: Dict = None, obj: Dict = None):
        super().__init__(cfg, obj)
        # This is a standalone task that doesn't need the full pipeline
        self.shoud_recogn = False
        self.shoud_trans = False
        self.shoud_dubbing = False
        self.shoud_hebing = False
        
        # Set up file paths
        self.srt_file = self.cfg['srt_path']
        self.video_file = self.cfg['video_path']
        self.styled_subtitle_file = None
        self.dubbing_audio_file = None
        
    def prepare(self):
        """Main execution method"""
        try:
            self._signal(text="Starting subtitle style application task")

            # Step 1: Setup and verify files
            self._setup_files()
            if self._exit():
                return
                
            # Step 2: Apply subtitle styling
            self._apply_subtitle_styling()
            if self._exit():
                return
                
            # Step 3: Generate dubbing if enabled
            if self.cfg.get('dubbing_settings') and self.cfg['dubbing_settings'].get('enable_dubbing'):
                self._generate_dubbing()
                if self._exit():
                    return
                    
            # Step 4: Merge video with styled subtitles and dubbing
            self._merge_video()
            if self._exit():
                return
                
            self._signal(text="Task completed successfully", type="succeed")
            self.hasend = True
            
        except Exception as e:
            self._signal(text=f"Error: {str(e)}", type="error")
            self.hasend = True
            raise
            
    def _setup_files(self):
        """Setup and verify local file paths"""
        self._signal(text="Setting up files...")
        self.precent = 10
        
        try:
            # Verify files exist
            if not Path(self.srt_file).exists():
                raise Exception(f"SRT file not found: {self.srt_file}")
            if not Path(self.video_file).exists():
                raise Exception(f"Video file not found: {self.video_file}")
                
            self._signal(text="Files verified")
            self.precent = 20
            
        except Exception as e:
            raise Exception(f"Failed to setup files: {str(e)}")
            
    def _apply_subtitle_styling(self):
        """Apply styling to subtitles"""
        self._signal(text="Applying subtitle styling...")
        self.precent = 40
        
        try:
            subtitle_style = self.cfg['subtitle_style']
            subtitle_layout = self.cfg['subtitle_layout']
            
            # Create ASS file with custom styling
            self.styled_subtitle_file = f"{self.cfg['cache_folder']}/styled_subtitle.ass"
            
            if subtitle_layout == 'double':
                # Create double subtitle layout (primary + secondary)
                self._create_double_subtitle_ass()
            else:
                # Create single subtitle layout (primary only)
                self._create_single_subtitle_ass()
                    
            self._signal(text="Subtitle styling applied")
            self.precent = 50
            
        except Exception as e:
            raise Exception(f"Failed to apply subtitle styling: {str(e)}")
            
    def _create_single_subtitle_ass(self):
        """Create ASS file with single subtitle styling"""
        try:
            primary_style = self.cfg['subtitle_style']['primary']
            
            # Convert SRT to basic ASS first
            maxlen = 40
            tools.srt2ass(self.srt_file, self.styled_subtitle_file, maxlen)
            
            # Read and modify ASS content
            with open(self.styled_subtitle_file, 'r', encoding='utf-8') as f:
                ass_content = f.readlines()
            
            # Find and update the Style line
            for i, line in enumerate(ass_content):
                if line.startswith('Style: Default,'):
                    # Create new style line with primary settings
                    style_parts = line.strip().split(',')
                    if len(style_parts) >= 23:
                        style_parts[1] = primary_style['font_family']  # Fontname
                        style_parts[2] = str(primary_style['font_size'])  # Fontsize
                        style_parts[3] = primary_style['color']  # PrimaryColour
                        style_parts[5] = primary_style['stroke_color'] if primary_style['show_stroke'] else primary_style['color']  # OutlineColour
                        style_parts[6] = primary_style['background_color'] if primary_style['show_background'] else '&H0'  # BackColour
                        style_parts[16] = str(primary_style['stroke_width']) if primary_style['show_stroke'] else '0'  # Outline
                        style_parts[17] = '1' if primary_style['show_shadow'] else '0'  # Shadow
                        style_parts[19] = '2'  # Alignment (bottom)
                        style_parts[22] = str(primary_style['margin_v'])  # MarginV
                        
                        ass_content[i] = ','.join(style_parts) + '\n'
                        break
            
            # Write back the modified content
            with open(self.styled_subtitle_file, 'w', encoding='utf-8') as f:
                f.writelines(ass_content)
                
        except Exception as e:
            config.logger.error(f"Error creating single subtitle ASS: {str(e)}")
            raise
            
    def _create_double_subtitle_ass(self):
        """Create ASS file with double subtitle styling (primary + secondary)"""
        try:
            primary_style = self.cfg['subtitle_style']['primary']
            secondary_style = self.cfg['subtitle_style']['secondary']
            
            # Read SRT content
            srt_subtitles = tools.get_subtitle_from_srt(self.srt_file)
            
            # Create ASS header
            ass_content = [
                "[Script Info]\n",
                "Title: Double Subtitle\n",
                "ScriptType: v4.00+\n",
                "PlayResX: 1920\n",
                "PlayResY: 1080\n",
                "ScaledBorderAndShadow: yes\n",
                "\n",
                "[V4+ Styles]\n",
                "Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n"
            ]
            
            # Add primary style
            primary_outline = str(primary_style['stroke_width']) if primary_style['show_stroke'] else '0'
            primary_shadow = '1' if primary_style['show_shadow'] else '0'
            primary_back_color = primary_style['background_color'] if primary_style['show_background'] else '&H0'
            
            primary_style_line = f"Style: Primary,{primary_style['font_family']},{primary_style['font_size']},{primary_style['color']},{primary_style['color']},{primary_style['stroke_color']},{primary_back_color},0,0,0,0,100,100,0,0,1,{primary_outline},{primary_shadow},2,10,10,{primary_style['margin_v']},1\n"
            ass_content.append(primary_style_line)
            
            # Add secondary style
            secondary_outline = str(secondary_style['stroke_width']) if secondary_style['show_stroke'] else '0'
            secondary_shadow = '1' if secondary_style['show_shadow'] else '0'
            secondary_back_color = secondary_style['background_color'] if secondary_style['show_background'] else '&H0'
            
            secondary_style_line = f"Style: Secondary,{secondary_style['font_family']},{secondary_style['font_size']},{secondary_style['color']},{secondary_style['color']},{secondary_style['stroke_color']},{secondary_back_color},0,0,0,0,100,100,0,0,1,{secondary_outline},{secondary_shadow},2,10,10,{secondary_style['margin_v']},1\n"
            ass_content.append(secondary_style_line)
            
            # Add events header
            ass_content.extend([
                "\n",
                "[Events]\n",
                "Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n"
            ])
            
            # Add dialogue lines (assuming we want to show both primary and secondary for each subtitle)
            for subtitle in srt_subtitles:
                start_time = subtitle['startraw'].replace(',', '.')
                end_time = subtitle['endraw'].replace(',', '.')
                text = subtitle['text'].replace('\n', '\\N')
                
                # Add primary subtitle
                primary_line = f"Dialogue: 0,{start_time},{end_time},Primary,,0,0,0,,{text}\n"
                ass_content.append(primary_line)
                
                # Add secondary subtitle (could be translation or same text with different styling)
                secondary_line = f"Dialogue: 0,{start_time},{end_time},Secondary,,0,0,0,,{text}\n"
                ass_content.append(secondary_line)
            
            # Write ASS file
            with open(self.styled_subtitle_file, 'w', encoding='utf-8') as f:
                f.writelines(ass_content)
                
        except Exception as e:
            config.logger.error(f"Error creating double subtitle ASS: {str(e)}")
            raise

    def recogn(self):
        """Required by BaseTask - not used"""
        pass

    def trans(self):
        """Required by BaseTask - not used"""
        pass

    def dubbing(self):
        """Required by BaseTask - not used"""
        pass

    def align(self):
        """Required by BaseTask - not used"""
        pass

    def assembling(self):
        """Required by BaseTask - called after prepare"""
        pass
        
    def task_done(self):
        """Required by BaseTask - cleanup and finalization"""
        pass
        
    def _generate_dubbing(self):
        """Generate dubbing audio from subtitles"""
        self._signal(text="Generating dubbing audio...")
        self.precent = 60

        try:
            dubbing_settings = self.cfg['dubbing_settings']

            # Create dubbing configuration
            dubbing_cfg = {
                'target_sub': self.srt_file,
                'target_wav': f"{self.cfg['cache_folder']}/dubbing_audio.wav",
                'tts_type': dubbing_settings.get('tts_type', 0),
                'voice_role': dubbing_settings.get('voice_role', ''),
                'voice_rate': dubbing_settings.get('voice_rate', '+0%'),
                'volume': dubbing_settings.get('volume', '+0%'),
                'pitch': dubbing_settings.get('pitch', '+0Hz'),
                'target_language_code': dubbing_settings.get('target_language', 'zh-cn'),
                'cache_folder': self.cfg['cache_folder'],
                'uuid': self.uuid,
                'basename': 'subtitle_dubbing',
                'noextname': 'subtitle_dubbing',
                'voice_autorate': False,
                'out_ext': 'wav'
            }

            # Use existing dubbing functionality
            dubbing_task = DubbingSrt(dubbing_cfg)
            dubbing_task.dubbing()
            dubbing_task.align()

            self.dubbing_audio_file = dubbing_cfg['target_wav']
            self._signal(text="Dubbing audio generated")
            self.precent = 70

        except Exception as e:
            raise Exception(f"Failed to generate dubbing: {str(e)}")

    def _merge_video(self):
        """Merge video with styled subtitles and dubbing"""
        self._signal(text="Merging video with subtitles and audio...")
        self.precent = 80

        try:
            output_file = f"{self.cfg['target_dir']}/output_video.mp4"

            # Build ffmpeg command
            cmd = ['-y', '-i', self.video_file]

            # Add dubbing audio if available
            if hasattr(self, 'dubbing_audio_file') and self.dubbing_audio_file and Path(self.dubbing_audio_file).exists():
                cmd.extend(['-i', self.dubbing_audio_file])

            # Set video codec
            cmd.extend(['-c:v', 'libx264'])

            # Handle hard subtitles - embed using ASS file
            if self.styled_subtitle_file and Path(self.styled_subtitle_file).exists():
                # Use subtitles filter to embed hard subtitles
                escaped_subtitle_path = self.styled_subtitle_file.replace(':', '\\:')
                subtitle_filter = f"subtitles={escaped_subtitle_path}"
                cmd.extend(['-vf', subtitle_filter])

            # Audio settings
            if hasattr(self, 'dubbing_audio_file') and self.dubbing_audio_file and Path(self.dubbing_audio_file).exists():
                cmd.extend(['-c:a', 'aac', '-b:a', '192k'])
                # Map video from first input and audio from second input
                cmd.extend(['-map', '0:v:0', '-map', '1:a:0'])
            else:
                # Copy original audio if no dubbing
                cmd.extend(['-c:a', 'copy'])

            # Quality settings
            cmd.extend(['-crf', str(config.settings.get('crf', 25))])
            cmd.extend(['-preset', config.settings.get('preset', 'medium')])
            cmd.extend(['-movflags', '+faststart'])

            # Output file
            cmd.append(output_file)

            # Execute ffmpeg command
            tools.runffmpeg(cmd)

            self._signal(text="Video processing completed")
            self.precent = 95

            # Verify output file was created
            if Path(output_file).exists():
                self._signal(text="Output video created successfully")
                self.precent = 100
            else:
                raise Exception("Output video file was not created")

        except Exception as e:
            raise Exception(f"Failed to merge video: {str(e)}")

    def _exit(self):
        """Override exit condition to avoid premature exit"""
        # Only exit if explicitly requested or task has ended
        return config.exit_soft or self.hasend
