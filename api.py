if __name__ == '__main__':
    print('API ...')
    import json
    import multiprocessing
    import random
    import re
    import shutil
    import threading
    import time
    from pathlib import Path

    from flask import Flask, request, jsonify, send_from_directory
    from flask_cors import CORS
    from waitress import serve
    import hashlib
    import os

    from videotrans.configure import config
    from videotrans.task._dubbing import DubbingSrt
    from videotrans.task._speech2text import SpeechToText
    from videotrans.task._translate_srt import TranslateSrt
    from videotrans.task.job import start_thread
    from videotrans.task.trans_create import TransCreate
    from videotrans.task._base import BaseTask
    from videotrans.util import tools
    from videotrans import tts as tts_model, translator, recognition
    from functools import wraps
    import uuid
    import google.generativeai as genai
    import requests
    import urllib.parse

    ###### 配置信息
    #### api文档 https://pyvideotrans.com/api-cn
    config.exec_mode='api'
    ROOT_DIR = config.ROOT_DIR
    HOST = "127.0.0.1"
    PORT = 9011
    if Path(ROOT_DIR+'/host.txt').is_file():
        host_str=Path(ROOT_DIR+'/host.txt').read_text(encoding='utf-8').strip()
        host_str=re.sub(r'https?://','',host_str).split(':')
        if len(host_str)>0:
            HOST=host_str[0]
        if len(host_str)==2:
            PORT=int(host_str[1])

    # 存储生成的文件和进度日志
    API_RESOURCE='apidata'
    TARGET_DIR = ROOT_DIR + f'/{API_RESOURCE}'
    Path(TARGET_DIR).mkdir(parents=True, exist_ok=True)
    # 进度日志
    PROCESS_INFO = TARGET_DIR + '/processinfo'
    if Path(PROCESS_INFO).is_dir():
        shutil.rmtree(PROCESS_INFO)
    Path(PROCESS_INFO).mkdir(parents=True, exist_ok=True)
    # url前缀
    URL_PREFIX = f"http://{HOST}:{PORT}/{API_RESOURCE}"
    config.exit_soft = False
    # 停止 结束 失败状态
    end_status_list = ['error', 'succeed', 'end', 'stop']
    #日志状态
    logs_status_list = ['logs']

    ######################

    app = Flask(__name__, static_folder=TARGET_DIR)
    CORS(app)

    # 第1个接口 /tts
    """
    根据字幕合成配音接口
    
    请求数据类型: Content-Type:application
    
    请求参数：
    
    name:必须参数，字符串类型，需要配音的srt字幕的绝对路径(需同本软件在同一设备)，或者直接传递合法的srt字幕格式内容
    tts_type:必须参数，数字类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS",3=302.AI,4="FishTTS",5="Azure-TTS",
        6="GPT-SoVITS",7="clone-voice",8="OpenAI TTS",9="Elevenlabs.io",10="Google TTS",11="自定义TTS API"
    voice_role:必须参数，字符串类型，对应配音渠道的角色名，edge-tts/azure-tts/302.ai(azure模型)时目标语言不同，角色名也不同，具体见底部
    target_language:必须参数，字符串类型，需要配音的语言类型代码，即所传递的字幕文字语言代码，可选值 简体中文zh-cn，繁体zh-tw，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
    voice_rate:可选参数，字符串类型，语速加减值，格式为：加速`+数字%`，减速`-数字%`
    volume:可选参数，字符串类型，音量变化值(仅配音渠道为edge-tts生效)，格式为 增大音量`+数字%`，降低音量`-数字%`
    pitch:可选参数，字符串类型，音调变化值(仅配音渠道为edge-tts生效)，格式为 调大音调`+数字Hz`,降低音量`-数字Hz`
    out_ext:可选参数，字符串类型，输出配音文件类型，mp3|wav|flac|aac,默认wav
    voice_autorate:可选参数，布尔类型，默认False，是否自动加快语速，以便与字幕对齐
    
    返回数据：
    返回类型：json格式，
    成功时返回，可根据task_id通过 task_status 获取任务进度
    {"code":0,"msg":"ok","task_id":任务id}
    
    失败时返回
    {"code":1,"msg":"错误信息"}
    
    
    请求示例
    ```
    def test_tts():
        res=requests.post("http://127.0.0.1:9011/tts",json={
        "name":"C:/users/<USER>/videos/zh0.srt",
        "voice_role":"zh-CN-YunjianNeural",
        "target_language_code":"zh-cn",
        "voice_rate":"+0%",
        "volume":"+0%",
        "pitch":"+0Hz",
        "tts_type":"0",
        "out_ext":"mp3",
        "voice_autorate":True,
        })
        print(res.json())
    ```
    """
    @app.route('/tts', methods=['POST'])
    def tts():
        data = request.json
        # 从请求数据中获取参数
        name = data.get('name', '').strip()
        if not name:
            return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})
        is_srt=True
        if name.find("\n") == -1 and name.endswith('.srt'):
            if not Path(name).exists():
                return jsonify({"code": 1, "msg": f"The file {name} is not exist"})
        else:
            tmp_file = config.TEMP_DIR + f'/tts-srt-{time.time()}-{random.randint(1, 9999)}.srt'
            is_srt=tools.is_srt_string(name)
            Path(tmp_file).write_text(tools.process_text_to_srt_str(name) if not is_srt else name, encoding='utf-8')
            name = tmp_file

        cfg={
            "name":name,
            "voice_role":data.get("voice_role"),
            "target_language_code":data.get('target_language_code'),
            "tts_type":int(data.get('tts_type',0)),
            "voice_rate":data.get('voice_rate',"+0%"),
            "volume":data.get('volume',"+0%"),
            "pitch":data.get('pitch',"+0Hz"),
            "out_ext":data.get('out_ext',"mp3"),
            "voice_autorate":bool(data.get('voice_autorate',False)) if is_srt else False,
        }
        is_allow_lang=tts_model.is_allow_lang(langcode=cfg['target_language_code'],tts_type=cfg['tts_type'])
        if is_allow_lang is not True:
            return jsonify({"code":4,"msg":is_allow_lang})
        is_input_api=tts_model.is_input_api(tts_type=cfg['tts_type'],return_str=True)
        if is_input_api is not True:
            return jsonify({"code":5,"msg":is_input_api})


        obj = tools.format_video(name, None)
        obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
        obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
        Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
        cfg.update(obj)

        config.box_tts = 'ing'
        trk = DubbingSrt(cfg)
        config.dubb_queue.append(trk)
        tools.set_process(text=f"Currently in queue No.{len(config.dubb_queue)}",uuid=obj['uuid'])
        return jsonify({'code': 0, 'task_id': obj['uuid']})


    # 第2个接口 /translate_srt
    """
    字幕翻译接口
    
    请求参数:
    类型 Content-Type:application/json
    
    请求数据:
    name:必须参数，字符串类型，需要翻译的srt字幕的绝对路径(需同本软件在同一设备)，或者直接传递合法的srt字幕格式内容
    translate_type：必须参数，整数类型，翻译渠道
    target_language:必须参数，字符串类型，要翻译到的目标语言代码。可选值 简体中文zh-cn，繁体zh-tw，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
    source_code:可选参数，字符串类型，原始字幕语言代码，可选同上
    
    返回数据
    返回类型：json格式，
    成功时返回，可根据task_id通过 task_status 获取任务进度
    {"code":0,"msg":"ok","task_id":任务id}
    
    失败时返回
    {"code":1,"msg":"错误信息"}
    
    请求示例
    ```
    def test_translate_srt():
        res=requests.post("http://127.0.0.1:9011/translate_srt",json={
        "name":"C:/users/<USER>/videos/zh0.srt",
        "target_language":"en",
        "translate_type":0
        })
        print(res.json())
    ```
    
    """
    @app.route('/translate_srt', methods=['POST'])
    def translate_srt():
        data = request.json
        # 从请求数据中获取参数
        name = data.get('name', '').strip()
        if not name:
            return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})
        name = get_local_file_path(request, name)

        if name.find("\n") == -1  and name.endswith('.srt'):
            if not Path(name).exists():
                return jsonify({"code": 1, "msg": f"The file {name} is not exist"})
        else:
            tmp_file = config.TEMP_DIR + f'/trans-srt-{time.time()}-{random.randint(1, 9999)}.srt'
            is_srt=tools.is_srt_string(name)
            Path(tmp_file).write_text(tools.process_text_to_srt_str(name) if not is_srt else name, encoding='utf-8')
            name = tmp_file

        cfg = {
            "translate_type": int(data.get('translate_type', 12)),
            "text_list": tools.get_subtitle_from_srt(name),
            "target_code": data.get('target_language'),
            "source_code": data.get('source_code', '')
        }
        is_allow=translator.is_allow_translate(translate_type=cfg['translate_type'],show_target=cfg['target_code'],return_str=True)
        if is_allow is not True:
            return jsonify({"code":5,"msg":is_allow})
        obj = tools.format_video(name, None)
        obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
        obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
        Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
        cfg.update(obj)

        config.box_trans = 'ing'
        trk = TranslateSrt(cfg)
        config.trans_queue.append(trk)
        tools.set_process(text=f"Currently in queue No.{len(config.trans_queue)}",uuid=obj['uuid'])
        return jsonify({'code': 0, 'task_id': obj['uuid']})


    # 第3个接口 /recogn
    """
    语音识别、音视频转字幕接口
    
    请求参数:
    类型 Content-Type:application/json
    
    请求数据:
    name:必须参数，字符串类型，需要翻译的音频或视频的绝对路径(需同本软件在同一设备)
    recogn_type:必须参数，数字类型，语音识别模式，0=faster-whisper本地模型识别，1=openai-whisper本地模型识别，2=Google识别api，3=zh_recogn中文识别，4=豆包模型识别，5=自定义识别API，6=OpenAI识别API
    model_name:必须参数faster-whisper和openai-whisper模式时的模型名字
    detect_language:必须参数，字符串类型，音视频中人类说话语言。中文zh，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
    split_type：可选参数，字符串类型，默认all：整体识别，可选avg：均等分割
    is_cuda:可选参数，布尔类型，是否启用CUDA加速，默认False
    
    返回数据
    返回类型：json格式，
    成功时返回，可根据task_id通过 task_status 获取任务进度
    {"code":0,"msg":"ok","task_id":任务id}
    
    失败时返回
    {"code":1,"msg":"错误信息"}
    
    示例
    def test_recogn():
        res=requests.post("http://127.0.0.1:9011/recogn",json={
        "name":"C:/Users/<USER>/Videos/10ass.mp4",
        "recogn_type":0,
        "split_type":"all",
        "model_name":"tiny",
        "is_cuda":False,
        "detect_language":"zh",
        })
        print(res.json())
    
    """
    @app.route('/recogn', methods=['POST'])
    def recogn():
        data = request.json
        # 从请求数据中获取参数
        name = data.get('name', '').strip()
        if not name:
            return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})

        name = get_local_file_path(request, name)

        if not Path(name).is_file():
            return jsonify({"code": 1, "msg": f"The file {name} is not exist"})

        cfg = {
            "recogn_type": int(data.get('recogn_type', 6)),
            "split_type": data.get('split_type', 'all'),
            "model_name": data.get('model_name', 'whisper-1'),
            "is_cuda": bool(data.get('is_cuda', False)),
            "detect_language": data.get('detect_language', '')
        }

        is_allow=recognition.is_allow_lang(langcode=cfg['detect_language'],recogn_type=cfg['recogn_type'])
        if is_allow is not True:
            return jsonify({"code":5,"msg":is_allow})

        is_input=recognition.is_input_api(recogn_type=cfg['recogn_type'],return_str=True)
        if is_input is not True:
            return jsonify({"code":5,"msg":is_input})


        obj = tools.format_video(name, None)
        obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
        obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
        Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
        cfg.update(obj)
        config.box_recogn = 'ing'
        trk = SpeechToText(cfg)
        config.prepare_queue.append(trk)
        tools.set_process(text=f"Currently in queue No.{len(config.prepare_queue)}",uuid=obj['uuid'])
        return jsonify({'code': 0, 'task_id': obj['uuid']})


    # 第4个接口
    """
    视频完整翻译接口
    
    
    请求参数:
    类型 Content-Type:application/json
    
    请求数据:
    name:必须参数，字符串类型，需要翻译的音频或视频的绝对路径(需同本软件在同一设备)
    recogn_type:必须参数，数字类型，语音识别模式，0=faster-whisper本地模型识别，1=openai-whisper本地模型识别，2=Google识别api，3=zh_recogn中文识别，4=豆包模型识别，5=自定义识别API，6=OpenAI识别API
    model_name:必须参数faster-whisper和openai-whisper模式时的模型名字
    split_type：可选参数，字符串类型，默认all：整体识别，可选avg：均等分割
    is_cuda:可选参数，布尔类型，是否启用CUDA加速，默认False
    translate_type：必须参数，整数类型，翻译渠道
    target_language:必须参数，字符串类型，要翻译到的目标语言代码。可选值 简体中文zh-cn，繁体zh-tw，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
    source_language:可选参数，字符串类型，原始字幕语言代码，可选同上
    tts_type:必须参数，数字类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS",3=302.AI,4="FishTTS",5="Azure-TTS",
        6="GPT-SoVITS",7="clone-voice",8="OpenAI TTS",9="Elevenlabs.io",10="Google TTS",11="自定义TTS API"
    voice_role:必须参数，字符串类型，对应配音渠道的角色名，edge-tts/azure-tts/302.ai(azure模型)时目标语言不同，角色名也不同，具体见底部
    voice_rate:可选参数，字符串类型，语速加减值，格式为：加速`+数字%`，减速`-数字%`
    volume:可选参数，字符串类型，音量变化值(仅配音渠道为edge-tts生效)，格式为 增大音量`+数字%`，降低音量`-数字%`
    pitch:可选参数，字符串类型，音调变化值(仅配音渠道为edge-tts生效)，格式为 调大音调`+数字Hz`,降低音量`-数字Hz`
    out_ext:可选参数，字符串类型，输出配音文件类型，mp3|wav|flac|aac,默认wav
    voice_autorate:可选参数，布尔类型，默认False，是否自动加快语速，以便与字幕对齐
    subtitle_type:可选参数，整数类型，默认0，字幕嵌入类型，0=不嵌入字幕，1=嵌入硬字幕，2=嵌入软字幕，3=嵌入双硬字幕，4=嵌入双软字幕
    append_video：可选参数，布尔类型，默认False，如果配音后音频时长大于视频，是否延长视频末尾
    only_video:可选参数，布尔类型，默认False，是否只生成视频文件，不生成字幕音频等
    
    返回数据
    返回类型：json格式，
    成功时返回，可根据task_id通过 task_status 获取任务进度
    {"code":0,"msg":"ok","task_id":任务id}
    
    失败时返回
    {"code":1,"msg":"错误信息"}
    
    示例
    def test_trans_video():
        res=requests.post("http://127.0.0.1:9011/trans_video",json={
        "name":"C:/Users/<USER>/Videos/10ass.mp4",
    
        "recogn_type":0,
        "split_type":"all",
        "model_name":"tiny",
    
        "translate_type":0,
        "source_language":"zh-cn",
        "target_language":"en",
    
        "tts_type":0,
        "voice_role":"zh-CN-YunjianNeural",
        "voice_rate":"+0%",
        "volume":"+0%",
        "pitch":"+0Hz",
        "voice_autorate":True,
        "video_autorate":True,
    
        "is_separate":False,
        "back_audio":"",
        
        "subtitle_type":1,
        "append_video":False,
    
        "is_cuda":False,
        })
        print(res.json())
    
    """
    @app.route('/trans_video', methods=['POST'])
    def trans_video():
        data = request.json
        name = data.get('name', '')
        if not name:
            return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})
        if not Path(name).exists():
            return jsonify({"code": 1, "msg": f"The file {name} is not exist"})

        cfg = {
            # 通用
            "name": name,

            "is_separate": bool(data.get('is_separate', False)),
            "back_audio": data.get('back_audio', ''),

            # 识别
            "recogn_type": int(data.get('recogn_type', 0)),
            "split_type": data.get('split_type','all'),
            "model_name": data.get('model_name','tiny'),
            "cuda": bool(data.get('is_cuda',False)),

            "subtitles": data.get("subtitles", ""),

            # 翻译
            "translate_type": int(data.get('translate_type', 0)),
            "target_language": data.get('target_language'),
            "source_language": data.get('source_language'),

            # 配音
            "tts_type": int(data.get('tts_type', 0)),
            "voice_role": data.get('voice_role',''),
            "voice_rate": data.get('voice_rate','+0%'),
            "voice_autorate": bool(data.get('voice_autorate', False)),
            "video_autorate": bool(data.get('video_autorate', False)),
            "volume": data.get('volume','+0%'),
            "pitch": data.get('pitch','+0Hz'),

            "subtitle_type": int(data.get('subtitle_type', 0)),
            "append_video": bool(data.get('append_video', False)),

            "is_batch": True,
            "app_mode": "biaozhun",

            "only_video": bool(data.get('only_video', False))

        }
        if not cfg['subtitles']:
            is_allow = recognition.is_allow_lang(langcode=cfg['target_language'], recogn_type=cfg['recogn_type'])
            if is_allow is not True:
                return jsonify({"code": 5, "msg": is_allow})

            is_input = recognition.is_input_api(recogn_type=cfg['recogn_type'], return_str=True)
            if is_input is not True:
                return jsonify({"code": 5, "msg": is_input})
        if cfg['source_language'] != cfg['target_language']:
            is_allow=translator.is_allow_translate(translate_type=cfg['translate_type'],show_target=cfg['target_language'],return_str=True)
            if is_allow is not True:
                return jsonify({"code":5,"msg":is_allow})

        if cfg['voice_role'] and cfg['voice_role'].lower()!='no' and cfg['target_language']:
            is_allow_lang = tts_model.is_allow_lang(langcode=cfg['target_language'], tts_type=cfg['tts_type'])
            if is_allow_lang is not True:
                return jsonify({"code": 4, "msg": is_allow_lang})
            is_input_api = tts_model.is_input_api(tts_type=cfg['tts_type'], return_str=True)
            if is_input_api is not True:
                return jsonify({"code": 5, "msg": is_input_api})



        obj = tools.format_video(name, None)
        obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
        obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
        Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
        cfg.update(obj)

        config.current_status = 'ing'
        trk = TransCreate(cfg)
        config.prepare_queue.append(trk)
        tools.set_process(text=f"Currently in queue No.{len(config.prepare_queue)}",uuid=obj['uuid'])
        #
        return jsonify({'code': 0, 'task_id': obj['uuid']})


    # 第5个接口 /voice_list
    """
    获取指定TTS服务的语音角色列表接口

    请求参数:
    类型 GET请求，URL参数

    请求数据:
    tts_type:必须参数，数字类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS",3=302.AI,4="FishTTS",5="Azure-TTS",
        6="GPT-SoVITS",7="clone-voice",8="OpenAI TTS",9="Elevenlabs.io",10="Google TTS",11="自定义TTS API",
        12="字节火山语音合成",13="F5-TTS",14="kokoro-TTS",15="Google Cloud TTS",16="Gemini TTS"
    language:可选参数，字符串类型，语言代码过滤，如zh、en、fr等

    返回数据
    返回类型：json格式，
    成功时返回
    {"code":0,"msg":"ok","data":{"service_name":"Edge-TTS","service_id":0,"voices":["voice1","voice2","voice3","voice4"]}}

    失败时返回
    {"code":1,"msg":"错误信息"}

    请求示例
    ```
    def test_voice_list():
        res = requests.get("http://127.0.0.1:9011/voice_list?tts_type=0&language=zh")
        print(res.json())
    ```

    """
    @app.route('/voices', methods=['GET'])
    def voice_list():
        tts_type = request.args.get('tts_type')
        language = request.args.get('language', '')

        if tts_type is None:
            return jsonify({"code": 1, "msg": "The parameter tts_type is required"})

        try:
            tts_type = int(tts_type)
        except (ValueError, TypeError):
            return jsonify({"code": 1, "msg": "The parameter tts_type must be a valid integer"})

        if tts_type < 0 or tts_type >= len(tts_model.TTS_NAME_LIST):
            return jsonify({"code": 1, "msg": f"Invalid tts_type. Must be between 0 and {len(tts_model.TTS_NAME_LIST)-1}"})

        service_name = tts_model.TTS_NAME_LIST[tts_type]
        all_voices = []

        try:
            if tts_type == tts_model.EDGE_TTS:
                voice_data = tools.get_edge_rolelist()
                if voice_data:
                    # voice_data is a dict like {"zh": ["voice1", "voice2"], "en": ["voice3"]}
                    for lang_voices in voice_data.values():
                        all_voices.extend(lang_voices)
            elif tts_type == tts_model.COSYVOICE_TTS:
                voice_data = tools.get_cosyvoice_role()
                if voice_data:
                    all_voices = list(voice_data.keys())
            elif tts_type == tts_model.CHATTTS:
                voice_data = list(config.ChatTTS_voicelist) if hasattr(config, 'ChatTTS_voicelist') else []
                all_voices = ["No"] + voice_data
            elif tts_type == tts_model.AI302_TTS:
                voice_data = tools.get_302ai()
                if voice_data:
                    # voice_data is a dict like {"zh": ["voice1"], "en": ["voice2"]}
                    for lang_voices in voice_data.values():
                        all_voices.extend(lang_voices)
            elif tts_type == tts_model.FISHTTS:
                voice_data = tools.get_fishtts_role()
                if voice_data:
                    all_voices = list(voice_data.keys())
            elif tts_type == tts_model.AZURE_TTS:
                voice_data = tools.get_azure_rolelist()
                if voice_data:
                    # voice_data is a dict like {"zh": ["voice1"], "en": ["voice2"]}
                    for lang_voices in voice_data.values():
                        all_voices.extend(lang_voices)
            elif tts_type == tts_model.GPTSOVITS_TTS:
                voice_data = tools.get_gptsovits_role()
                if voice_data:
                    all_voices = list(voice_data.keys())
            elif tts_type == tts_model.CLONE_VOICE_TTS:
                voice_data = config.params.get("clone_voicelist", ["clone"])
                all_voices = voice_data
            elif tts_type == tts_model.OPENAI_TTS:
                voice_data = config.params.get("openaitts_role", "alloy,ash,coral,echo,fable,onyx,nova,sage,shimmer,verse").split(',')
                all_voices = [v.strip() for v in voice_data if v.strip()]
            elif tts_type == tts_model.ELEVENLABS_TTS:
                voice_data = tools.get_elevenlabs_role()
                if voice_data:
                    all_voices = voice_data
            elif tts_type == tts_model.GOOGLE_TTS:
                all_voices = ["en-US-Standard-A", "en-US-Standard-B", "zh-CN-Standard-A", "zh-CN-Standard-B"]
            elif tts_type == tts_model.TTS_API:
                voice_data = config.params.get('ttsapi_voice_role', '').strip().split(',')
                all_voices = [v.strip() for v in voice_data if v.strip()] if voice_data and voice_data[0] else []
            elif tts_type == tts_model.VOLCENGINE_TTS:
                voice_data = tools.get_volcenginetts_rolelist()
                if voice_data:
                    all_voices = list(voice_data.keys())
            elif tts_type == tts_model.F5_TTS:
                voice_data = tools.get_f5tts_role()
                if voice_data:
                    all_voices = list(voice_data.keys())
            elif tts_type == tts_model.KOKORO_TTS:
                voice_data = tools.get_kokoro_rolelist()
                if voice_data:
                    # voice_data is a dict like {"en": ["voice1"], "zh": ["voice2"]}
                    for lang_voices in voice_data.values():
                        all_voices.extend(lang_voices)
            elif tts_type == tts_model.GOOGLECLOUD_TTS:
                all_voices = ["en-US-Standard-A", "en-US-Standard-B", "zh-CN-Standard-A", "zh-CN-Standard-B"]
            elif tts_type == tts_model.GEMINI_TTS:
                all_voices = ["Puck", "Charon", "Kore", "Fenrir", "zh-voice-1", "zh-voice-2"]
            else:
                all_voices = []

            # Remove duplicates and filter by language if specified
            unique_voices = list(dict.fromkeys(all_voices))  # Preserve order while removing duplicates

            # Filter by language if specified
            if language:
                filtered_voices = []
                language_lower = language.lower()
                for voice in unique_voices:
                    # Check if voice name contains language code or starts with language prefix
                    if (language_lower in voice.lower() or
                        voice.lower().startswith(language_lower) or
                        (language_lower == 'zh' and ('zh-' in voice.lower() or 'chinese' in voice.lower() or '中文' in voice)) or
                        (language_lower == 'en' and ('en-' in voice.lower() or 'english' in voice.lower())) or
                        (language_lower == 'ja' and ('ja-' in voice.lower() or 'japanese' in voice.lower() or '日语' in voice)) or
                        (language_lower == 'ko' and ('ko-' in voice.lower() or 'korean' in voice.lower() or '韩语' in voice))):
                        filtered_voices.append(voice)
                unique_voices = filtered_voices

            return jsonify({
                "code": 0,
                "msg": "ok",
                "data": {
                    "service_name": service_name,
                    "service_id": tts_type,
                    "voices": unique_voices
                }
            })

        except Exception as e:
            config.logger.error(f"Error getting voice list for tts_type {tts_type}: {str(e)}")
            return jsonify({"code": 1, "msg": f"Error retrieving voice list: {str(e)}"})


    # 第6个接口 /upload
    """
    文件上传接口

    请求参数:
    类型 Content-Type:multipart/form-data

    请求数据:
    file:必须参数，文件类型，要上传的文件

    返回数据
    返回类型：json格式，
    成功时返回
    {"code":0,"msg":"ok","data":{"file_path":"/tmp/upload/date_str/hash_filename.ext","url":"http://127.0.0.1:9011/file/date_str/hash_filename.ext"}}

    失败时返回
    {"code":1,"msg":"错误信息"}

    请求示例
    ```
    def test_upload():
        with open("test.mp4", "rb") as f:
            files = {"file": f}
            res = requests.post("http://127.0.0.1:9011/upload", files=files)
            print(res.json())
    ```

    """
    @app.route('/upload', methods=['POST'])
    def upload_file():
        # 检查是否有文件在请求中
        if 'file' not in request.files:
            return jsonify({"code": 1, "msg": "No file provided"})

        file = request.files['file']

        # 检查文件名是否为空
        if file.filename == '':
            return jsonify({"code": 1, "msg": "No file selected"})

        if file:
            try:
                # 创建上传目录
                date_str = tools.get_current_time_as_yymmddhhmmss(format='ymd')
                upload_dir = f'/tmp/upload/{date_str}'
                Path(upload_dir).mkdir(parents=True, exist_ok=True)

                # 获取文件扩展名
                original_filename = file.filename
                file_ext = Path(original_filename).suffix

                # 读取文件内容用于生成hash
                file_content = file.read()
                file.seek(0)  # 重置文件指针

                # 生成hash文件名 (使用文件内容 + 时间戳)
                hash_input = f"{file_content.hex()}-{time.time()}"
                file_hash = tools.get_md5(hash_input)
                hash_filename = f"{file_hash}{file_ext}"

                # 完整文件路径
                file_path = os.path.join(upload_dir, hash_filename)

                # 保存文件
                file.save(file_path)

                # 生成访问URL - 使用新的 /file/ 端点
                file_url = f"{request.scheme}://{request.host}/file/{date_str}/{hash_filename}"

                return jsonify({
                    "code": 0,
                    "msg": "ok",
                    "data": {
                        "file_path": file_path,
                        "url": file_url
                    }
                })

            except Exception as e:
                return jsonify({"code": 1, "msg": f"Upload failed: {str(e)}"})

        return jsonify({"code": 1, "msg": "Invalid file"})


    # Authentication decorator for OpenAI-compatible endpoints
    def require_auth(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                return jsonify({
                    "error": {
                        "message": "You didn't provide an API key.",
                        "type": "invalid_request_error",
                        "param": None,
                        "code": None
                    }
                }), 401

            if not auth_header.startswith('Bearer '):
                return jsonify({
                    "error": {
                        "message": "Invalid authorization header format. Expected 'Bearer <token>'.",
                        "type": "invalid_request_error",
                        "param": None,
                        "code": None
                    }
                }), 401

            # Extract the token (everything after 'Bearer ')
            token = auth_header[7:]
            if not token:
                return jsonify({
                    "error": {
                        "message": "You didn't provide an API key.",
                        "type": "invalid_request_error",
                        "param": None,
                        "code": None
                    }
                }), 401

            # For now, we'll accept any non-empty token
            # In production, you might want to validate against a database or config
            return f(*args, **kwargs)
        return decorated_function


    # OpenAI-compatible Chat Completions endpoint
    """
    OpenAI-compatible Chat Completions API

    请求参数:
    类型 Content-Type: application/json
    Authorization: Bearer API_KEY

    请求数据:
    model: 必须参数，字符串类型，模型名称 (支持 gemini-1.5-pro, gpt-4, gpt-3.5-turbo 等)
    messages: 必须参数，数组类型，对话消息列表
    temperature: 可选参数，浮点数类型，控制随机性 (0.0-2.0)
    max_tokens: 可选参数，整数类型，最大生成token数
    top_p: 可选参数，浮点数类型，核采样参数
    stream: 可选参数，布尔类型，是否流式返回 (暂不支持)

    返回数据:
    返回类型：json格式，OpenAI兼容格式
    成功时返回:
    {
        "id": "chatcmpl-xxx",
        "object": "chat.completion",
        "created": 1234567890,
        "model": "gemini-1.5-pro",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "回复内容"
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }

    失败时返回:
    {
        "error": {
            "message": "错误信息",
            "type": "invalid_request_error",
            "param": null,
            "code": null
        }
    }
    """
    @app.route('/v1/chat/completions', methods=['POST'])
    @require_auth
    def chat_completions():
        try:
            data = request.json
            if not data:
                return jsonify({
                    "error": {
                        "message": "Request body must be JSON",
                        "type": "invalid_request_error",
                        "param": None,
                        "code": None
                    }
                }), 400

            # Validate required parameters
            model = data.get('model', 'gemini-2.5-flash-preview-05-20')
            messages = data.get('messages')

            if not model:
                return jsonify({
                    "error": {
                        "message": "Missing required parameter: 'model'",
                        "type": "invalid_request_error",
                        "param": "model",
                        "code": None
                    }
                }), 400

            if not messages:
                return jsonify({
                    "error": {
                        "message": "Missing required parameter: 'messages'",
                        "type": "invalid_request_error",
                        "param": "messages",
                        "code": None
                    }
                }), 400

            if not isinstance(messages, list) or len(messages) == 0:
                return jsonify({
                    "error": {
                        "message": "'messages' must be a non-empty array",
                        "type": "invalid_request_error",
                        "param": "messages",
                        "code": None
                    }
                }), 400

            # Extract optional parameters
            temperature = data.get('temperature', 0.7)
            max_tokens = data.get('max_tokens', 4096)
            top_p = data.get('top_p', 1.0)
            stream = data.get('stream', False)

            if stream:
                return jsonify({
                    "error": {
                        "message": "Streaming is not currently supported",
                        "type": "invalid_request_error",
                        "param": "stream",
                        "code": None
                    }
                }), 400

            # Generate response based on model
            response_content = _generate_chat_response(model, messages, temperature, max_tokens, top_p)

            # Create OpenAI-compatible response
            completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
            created_timestamp = int(time.time())

            return jsonify({
                "id": completion_id,
                "object": "chat.completion",
                "created": created_timestamp,
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": _estimate_tokens(messages),
                    "completion_tokens": _estimate_tokens([{"content": response_content}]),
                    "total_tokens": _estimate_tokens(messages) + _estimate_tokens([{"content": response_content}])
                }
            })

        except Exception as e:
            config.logger.error(f"Chat completions error: {str(e)}")
            return jsonify({
                "error": {
                    "message": f"Internal server error: {str(e)}",
                    "type": "internal_server_error",
                    "param": None,
                    "code": None
                }
            }), 500


    # 静态文件服务 - 为上传的文件提供访问
    @app.route('/tmp/upload/<filename>')
    def uploaded_file(filename):
        upload_dir = '/tmp/upload'
        try:
            return send_from_directory(upload_dir, filename)
        except FileNotFoundError:
            return jsonify({"code": 1, "msg": "File not found"}), 404

    # 新的文件服务接口 - 支持路径参数访问上传的文件
    """
    文件访问接口

    请求参数:
    类型: GET

    请求路径:
    /file/<path:filepath> - filepath是相对于/tmp/upload/的文件路径，支持子目录

    返回数据:
    成功时返回文件内容
    失败时返回404或错误信息

    示例:
    GET /file/20241201/abc123.mp4  # 访问 /tmp/upload/20241201/abc123.mp4
    GET /file/abc123.mp4           # 访问 /tmp/upload/abc123.mp4
    """
    @app.route('/file/<path:filepath>')
    def serve_uploaded_file(filepath):
        # 安全检查：防止目录遍历攻击
        if '..' in filepath or filepath.startswith('/'):
            return jsonify({"code": 1, "msg": "Invalid file path"}), 400

        # 基础上传目录
        base_upload_dir = '/tmp/upload'

        # 构建完整文件路径
        full_file_path = os.path.join(base_upload_dir, filepath)

        # 检查文件是否存在
        if not os.path.isfile(full_file_path):
            return jsonify({"code": 1, "msg": "File not found"}), 404

        # 安全检查：确保文件在允许的目录内
        try:
            # 获取规范化的绝对路径
            real_file_path = os.path.realpath(full_file_path)
            real_base_path = os.path.realpath(base_upload_dir)

            # 检查文件是否在基础目录内
            if not real_file_path.startswith(real_base_path):
                return jsonify({"code": 1, "msg": "Access denied"}), 403
        except Exception:
            return jsonify({"code": 1, "msg": "Invalid file path"}), 400

        try:
            # 获取文件所在目录和文件名
            file_dir = os.path.dirname(full_file_path)
            filename = os.path.basename(full_file_path)

            # 使用Flask的send_from_directory发送文件
            return send_from_directory(file_dir, filename)
        except Exception as e:
            return jsonify({"code": 1, "msg": f"Error serving file: {str(e)}"}), 500


    # Helper functions for chat completions
    def _generate_chat_response(model, messages, temperature, max_tokens, top_p):
        """Generate chat response based on the specified model"""
        try:
            # Convert messages to text for processing
            conversation_text = ""
            for msg in messages:
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                conversation_text += f"{role}: {content}\n"

            # Route to appropriate AI service based on model name
            if 'gemini' in model.lower():
                return _generate_gemini_response(conversation_text, model, temperature, max_tokens)
            elif 'gpt' in model.lower() or 'chatgpt' in model.lower():
                return _generate_openai_response(conversation_text, model, temperature, max_tokens, top_p)
            elif 'claude' in model.lower():
                return _generate_claude_response(conversation_text, model, temperature, max_tokens)
            else:
                # Default to local LLM or first available service
                return _generate_default_response(conversation_text, model, temperature, max_tokens, top_p)

        except Exception as e:
            config.logger.error(f"Error generating chat response: {str(e)}")
            raise Exception(f"Failed to generate response: {str(e)}")

    def _generate_gemini_response(conversation_text, model, temperature, max_tokens):
        """Generate response using Gemini API"""
        try:
            # Check if Gemini is configured
            api_key = config.params.get('gemini_key')
            if not api_key:
                raise Exception("Gemini API key not configured. Please set gemini_key in configuration.")

            # Configure Gemini
            genai.configure(api_key=api_key)

            # Use the configured model or default
            gemini_model = config.params.get('gemini_model', 'gemini-1.5-pro')

            # Create model instance
            model_instance = genai.GenerativeModel(
                model_name=gemini_model,
                generation_config={
                    "max_output_tokens": max_tokens,
                    "temperature": temperature
                }
            )

            # Generate response
            response = model_instance.generate_content(conversation_text)

            if response and response.text:
                return response.text.strip()
            else:
                raise Exception("Empty response from Gemini")

        except Exception as e:
            config.logger.error(f"Gemini API error: {str(e)}")
            raise Exception(f"Gemini API error: {str(e)}")

    def _generate_openai_response(conversation_text, model, temperature, max_tokens, top_p):
        """Generate response using OpenAI API"""
        try:
            from openai import OpenAI
            import httpx

            # Check if OpenAI is configured
            api_key = config.params.get('chatgpt_key')
            api_url = config.params.get('chatgpt_api', 'https://api.openai.com/v1')

            if not api_key:
                raise Exception("OpenAI API key not configured. Please set chatgpt_key in configuration.")

            # Create OpenAI client
            client = OpenAI(
                api_key=api_key,
                base_url=api_url,
                http_client=httpx.Client(timeout=7200)
            )

            # Convert conversation text back to messages format
            messages = []
            lines = conversation_text.strip().split('\n')
            for line in lines:
                if ':' in line:
                    role, content = line.split(':', 1)
                    role = role.strip().lower()
                    content = content.strip()
                    if role in ['user', 'assistant', 'system']:
                        messages.append({"role": role, "content": content})

            # Generate response
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p
            )

            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                raise Exception("Empty response from OpenAI")

        except Exception as e:
            config.logger.error(f"OpenAI API error: {str(e)}")
            raise Exception(f"OpenAI API error: {str(e)}")

    def _generate_claude_response(conversation_text, model, temperature, max_tokens):
        """Generate response using Claude API"""
        try:
            # Check if Claude is configured
            api_key = config.params.get('claude_key')
            if not api_key:
                raise Exception("Claude API key not configured. Please set claude_key in configuration.")

            # For now, return a placeholder since Claude integration would need more setup
            return "Claude integration not fully implemented yet. Please use Gemini or OpenAI models."

        except Exception as e:
            config.logger.error(f"Claude API error: {str(e)}")
            raise Exception(f"Claude API error: {str(e)}")

    def _generate_default_response(conversation_text, model, temperature, max_tokens, top_p):
        """Generate response using local LLM or fallback"""
        try:
            # Try local LLM first
            api_url = config.params.get('localllm_api')
            api_key = config.params.get('localllm_key', '')

            if api_url:
                from openai import OpenAI
                import httpx

                client = OpenAI(
                    api_key=api_key,
                    base_url=api_url,
                    http_client=httpx.Client(timeout=7200)
                )

                # Convert conversation text back to messages format
                messages = []
                lines = conversation_text.strip().split('\n')
                for line in lines:
                    if ':' in line:
                        role, content = line.split(':', 1)
                        role = role.strip().lower()
                        content = content.strip()
                        if role in ['user', 'assistant', 'system']:
                            messages.append({"role": role, "content": content})

                response = client.chat.completions.create(
                    model=config.params.get('localllm_model', model),
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p
                )

                if response.choices and response.choices[0].message:
                    return response.choices[0].message.content.strip()

            # Fallback response
            return f"I'm a chat assistant. You said: {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"

        except Exception as e:
            config.logger.error(f"Default response generation error: {str(e)}")
            # Final fallback
            return "I'm sorry, I'm having trouble generating a response right now. Please check the API configuration."

    def _estimate_tokens(messages):
        """Rough estimation of token count"""
        total_chars = 0
        for msg in messages:
            if isinstance(msg, dict):
                content = msg.get('content', '')
            else:
                content = str(msg)
            total_chars += len(content)
        # Rough approximation: 1 token ≈ 4 characters
        return max(1, total_chars // 4)


    # 第7个接口 /v2/apply_subtitle_style
    """
    应用字幕样式和配音到视频接口

    请求参数:
    类型 Content-Type:application/json

    请求数据:
    srt_url:必须参数，字符串类型，SRT字幕文件的URL地址或本地路径
    video_url:必须参数，字符串类型，视频文件的URL地址或本地路径
    voiceSeparation:可选参数，布尔类型，是否进行人声分离，默认false
    sourceLanguage:可选参数，字符串类型，源语言代码，默认"en"
    targetLanguage:可选参数，字符串类型，目标语言代码，默认"zh-cn"
    subtitleLayout:可选参数，字符串类型，字幕布局，"single"=单字幕，"double"=双字幕，默认"single"
    subtitleStyle:可选参数，对象类型，字幕样式设置
        fontSize:可选参数，整数类型，主字幕字体大小，默认15
        fontFamily:可选参数，字符串类型，主字幕字体名称，默认"NotoSansCJK-Regular"
        primaryColor:可选参数，字符串类型，主字幕颜色，BGR格式如"&H00A6A6E2"
        primaryStrokeWidth:可选参数，整数类型，主字幕描边宽度，默认1
        shadowColor:可选参数，字符串类型，阴影颜色，默认"&H80000000"
        showPrimaryShadow:可选参数，布尔类型，是否显示主字幕阴影，默认false
        showPrimaryStroke:可选参数，布尔类型，是否显示主字幕描边，默认false
        primaryMarginV:可选参数，整数类型，主字幕垂直边距，默认0
        primaryBackgroundColor:可选参数，字符串类型，主字幕背景色，默认"&H33000000"
        showPrimaryBackground:可选参数，布尔类型，是否显示主字幕背景，默认false
        secondaryFontSize:可选参数，整数类型，副字幕字体大小，默认22
        secondaryFontFamily:可选参数，字符串类型，副字幕字体名称
        secondaryColor:可选参数，字符串类型，副字幕颜色，默认"&H0028E2A1"
        secondaryStrokeColor:可选参数，字符串类型，副字幕描边颜色，默认"&H000505E1"
        secondaryStrokeWidth:可选参数，整数类型，副字幕描边宽度，默认1
        secondaryBackgroundColor:可选参数，字符串类型，副字幕背景色，默认"&H000F0F4D"
        showSecondaryShadow:可选参数，布尔类型，是否显示副字幕阴影，默认false
        showSecondaryStroke:可选参数，布尔类型，是否显示副字幕描边，默认false
        showSecondaryBackground:可选参数，布尔类型，是否显示副字幕背景，默认true
        secondaryMarginV:可选参数，整数类型，副字幕垂直边距，默认32
    dubbing_settings:可选参数，对象类型，配音设置
        tts_type:可选参数，整数类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS"等，默认0
        voice_role:可选参数，字符串类型，语音角色名称
        voice_rate:可选参数，字符串类型，语速，格式"+0%"，默认"+0%"
        volume:可选参数，字符串类型，音量，格式"+0%"，默认"+0%"
        pitch:可选参数，字符串类型，音调，格式"+0Hz"，默认"+0Hz"

    返回数据
    返回类型：json格式，
    成功时返回，可根据task_id通过 task_status 获取任务进度
    {"code":0,"msg":"ok","task_id":任务id}

    失败时返回
    {"code":1,"msg":"错误信息"}

    请求示例
    ```
    def test_apply_subtitle_style():
        res=requests.post("http://127.0.0.1:9011/v2/apply_subtitle_style",json={
            "srt_url":"http://127.0.0.1:9011/file/subtitle.srt",
            "video_url":"http://127.0.0.1:9011/file/video.mp4",
            "voiceSeparation": true,
            "sourceLanguage": "en",
            "targetLanguage": "vi",
            "subtitleLayout": "double",
            "subtitleStyle": {
                "fontSize": 15,
                "fontFamily": "NotoSansCJK-Regular",
                "primaryColor": "&H00A6A6E2",
                "primaryStrokeWidth": 1,
                "shadowColor": "&H80000000",
                "showPrimaryShadow": false,
                "showPrimaryStroke": false,
                "primaryMarginV": 0,
                "primaryBackgroundColor": "&H33000000",
                "showPrimaryBackground": false,
                "secondaryFontSize": 22,
                "secondaryFontFamily": "NotoSansCJK-Regular",
                "secondaryColor": "&H0028E2A1",
                "secondaryStrokeColor": "&H000505E1",
                "secondaryStrokeWidth": 1,
                "secondaryBackgroundColor": "&H000F0F4D",
                "showSecondaryShadow": false,
                "showSecondaryStroke": false,
                "showSecondaryBackground": true,
                "secondaryMarginV": 32
            },
            "dubbing_settings":{
                "tts_type":0,
                "voice_role":"zh-CN-YunjianNeural",
                "voice_rate":"+0%",
                "volume":"+0%",
                "pitch":"+0Hz"
            }
        })
        print(res.json())
    ```
    """
    @app.route('/v2/apply_subtitle_style', methods=['POST'])
    def apply_subtitle_style():
        try:
            data = request.json
            if not data:
                return jsonify({"code": 1, "msg": "Request body must be JSON"})

            # 验证必需参数
            srt_url = data.get('srt_url', '').strip()
            video_url = data.get('video_url', '').strip()

            if not srt_url:
                return jsonify({"code": 1, "msg": "The parameter srt_url is required"})
            if not video_url:
                return jsonify({"code": 1, "msg": "The parameter video_url is required"})

            # Convert URLs to local file paths
            srt_path = get_local_file_path(request, srt_url)
            video_path = get_local_file_path(request, video_url)

            # 获取前端字幕设置
            subtitle_style = data.get('subtitleStyle', {})
            voice_separation = data.get('voiceSeparation', False)
            source_language = data.get('sourceLanguage', 'en')
            target_language = data.get('targetLanguage', 'zh-cn')
            subtitle_layout = data.get('subtitleLayout', 'single')  # single, double

            # 解析主字幕样式
            primary_font_family = subtitle_style.get('fontFamily', config.settings.get('fontname', '黑体'))
            primary_font_size = subtitle_style.get('fontSize', config.settings.get('fontsize', 16))
            primary_color = subtitle_style.get('primaryColor', config.settings.get('fontcolor', '&hffffff'))
            primary_stroke_color = subtitle_style.get('primaryStrokeColor', config.settings.get('fontbordercolor', '&h000000'))
            primary_stroke_width = subtitle_style.get('primaryStrokeWidth', 1)
            primary_background_color = subtitle_style.get('primaryBackgroundColor', '&H33000000')
            primary_margin_v = subtitle_style.get('primaryMarginV', 0)
            show_primary_stroke = subtitle_style.get('showPrimaryStroke', False)
            show_primary_background = subtitle_style.get('showPrimaryBackground', False)
            show_primary_shadow = subtitle_style.get('showPrimaryShadow', False)
            shadow_color = subtitle_style.get('shadowColor', '&H80000000')

            # 解析副字幕样式（用于双字幕布局）
            secondary_font_family = subtitle_style.get('secondaryFontFamily', primary_font_family)
            secondary_font_size = subtitle_style.get('secondaryFontSize', primary_font_size + 6)
            secondary_color = subtitle_style.get('secondaryColor', '&H0028E2A1')
            secondary_stroke_color = subtitle_style.get('secondaryStrokeColor', '&H000505E1')
            secondary_stroke_width = subtitle_style.get('secondaryStrokeWidth', 1)
            secondary_background_color = subtitle_style.get('secondaryBackgroundColor', '&H000F0F4D')
            secondary_margin_v = subtitle_style.get('secondaryMarginV', 32)
            show_secondary_stroke = subtitle_style.get('showSecondaryStroke', False)
            show_secondary_background = subtitle_style.get('showSecondaryBackground', True)
            show_secondary_shadow = subtitle_style.get('showSecondaryShadow', False)

            # 验证文件路径
            if not Path(srt_path).exists():
                return jsonify({"code": 1, "msg": f"SRT file not found: {srt_path}"})
            if not Path(video_path).exists():
                return jsonify({"code": 1, "msg": f"Video file not found: {video_path}"})

            # 获取配音设置
            dubbing_settings = data.get('dubbing_settings', {})
            enable_dubbing = bool(dubbing_settings)  # 如果提供了配音设置则启用配音
            tts_type = dubbing_settings.get('tts_type', 0) if enable_dubbing else 0
            voice_role = dubbing_settings.get('voice_role', '') if enable_dubbing else ''
            voice_rate = dubbing_settings.get('voice_rate', '+0%') if enable_dubbing else '+0%'
            volume = dubbing_settings.get('volume', '+0%') if enable_dubbing else '+0%'
            pitch = dubbing_settings.get('pitch', '+0Hz') if enable_dubbing else '+0Hz'

            # 验证配音设置
            if enable_dubbing and voice_role:
                is_allow_lang = tts_model.is_allow_lang(langcode=target_language, tts_type=tts_type)
                if is_allow_lang is not True:
                    return jsonify({"code": 4, "msg": is_allow_lang})
                is_input_api = tts_model.is_input_api(tts_type=tts_type, return_str=True)
                if is_input_api is not True:
                    return jsonify({"code": 5, "msg": is_input_api})

            # 创建任务配置
            obj = tools.format_video("temp_video", None)
            obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
            obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
            Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
            Path(obj['cache_folder']).mkdir(parents=True, exist_ok=True)

            cfg = {
                'srt_path': srt_path,
                'video_path': video_path,
                'voice_separation': voice_separation,
                'source_language': source_language,
                'target_language': target_language,
                'subtitle_layout': subtitle_layout,
                'subtitle_style': {
                    'primary': {
                        'font_family': primary_font_family,
                        'font_size': primary_font_size,
                        'color': primary_color,
                        'stroke_color': primary_stroke_color,
                        'stroke_width': primary_stroke_width,
                        'background_color': primary_background_color,
                        'margin_v': primary_margin_v,
                        'show_stroke': show_primary_stroke,
                        'show_background': show_primary_background,
                        'show_shadow': show_primary_shadow,
                        'shadow_color': shadow_color
                    },
                    'secondary': {
                        'font_family': secondary_font_family,
                        'font_size': secondary_font_size,
                        'color': secondary_color,
                        'stroke_color': secondary_stroke_color,
                        'stroke_width': secondary_stroke_width,
                        'background_color': secondary_background_color,
                        'margin_v': secondary_margin_v,
                        'show_stroke': show_secondary_stroke,
                        'show_background': show_secondary_background,
                        'show_shadow': show_secondary_shadow
                    }
                },
                'dubbing_settings': {
                    'enable_dubbing': enable_dubbing,
                    'tts_type': tts_type,
                    'voice_role': voice_role,
                    'voice_rate': voice_rate,
                    'volume': volume,
                    'pitch': pitch,
                    'target_language': target_language
                } if enable_dubbing else None
            }
            cfg.update(obj)

            # 创建并启动任务
            trk = ApplySubtitleStyleTask(cfg)
            config.prepare_queue.append(trk)
            tools.set_process(text=f"Currently in queue No.{len(config.prepare_queue)}", uuid=obj['uuid'])

            return jsonify({'code': 0, 'task_id': obj['uuid']})

        except Exception as e:
            config.logger.error(f"Apply subtitle style error: {str(e)}")
            return jsonify({"code": 1, "msg": f"Error: {str(e)}"})


    # 获取任务进度
    """
    根据任务id，获取当前任务的状态
    
    请求数据类型：优先GET中获取，不存在则从POST中获取，都不存在则从 json数据中获取
    
    请求参数: 
    task_id:必须，字符串类型
    
    返回:json格式数据
    code:-1=进行中，0=成功结束，>0=出错了
    msg:code为-1时为进度信息，code>0时为出错信息，成功时为ok
    data:仅当code==0成功时存在，是一个dict对象
        absolute_path是生成的文件列表list，每项均是一个文件的绝对路径
        url 是生成的文件列表list，每项均是一个可访问的url
    
    
    失败：{"code":1,"msg":"不存在该任务"}
    进行中：{"code":-1,"msg":"正在合成声音"} 
    成功: {"code":0,"msg":"ok","data":{"absolute_path":["/data/1.srt","/data/1.mp4"],"url":["http://127.0.0.1:9011/task_id/1.srt"]}}
    
    
    示例
    def test_task_status():
        res=requests.post("http://127.0.0.1:9011/task_status",json={
            "task_id":"06c238d250f0b51248563c405f1d7294"
        })
        print(res.json())
    
    {
      "code": 0,
      "data": {
        "absolute_path": [
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/10ass.mp4",
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/en.m4a",
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/en.srt",
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/end.srt.ass",
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.m4a",
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.srt",
          "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/文件说明.txt"
        ],
        "url": [
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/10ass.mp4",
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/en.m4a",
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/en.srt",
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/end.srt.ass",
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.m4a",
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.srt",
          "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/文件说明.txt"
        ]
      },
      "msg": "ok"
    }
    
    """
    @app.route('/task_status', methods=['POST', 'GET'])
    def task_status():
        # 1. 优先从 GET 请求参数中获取 task_id
        task_id = request.args.get('task_id')

        # 2. 如果 GET 参数中没有 task_id，再从 POST 表单中获取
        if task_id is None:
            task_id = request.form.get('task_id')

        # 3. 如果 POST 表单中也没有 task_id，再从 JSON 请求体中获取
        if task_id is None and request.is_json:
            task_id = request.json.get('task_id')
        if not task_id:
            return jsonify({"code": 1, "msg": "The parem  task_id is not set"})
        return _get_task_data(task_id)
        

    
    # 获取多个任务 前台 content-type:application/json, 数据 {task_id_list:[id1,id2,....]}
    @app.route('/task_status_list', methods=['POST', 'GET'])
    def task_status_list():
        # 1. 优先从 GET 请求参数中获取 task_id
        task_ids= request.json.get('task_id_list',[])
        if not task_ids or len(task_ids)<1:
            return jsonify({"code": 1, "msg": "Missing task_id"})
        
        return_data={}
        for task_id in task_ids:
            return_data[task_id]=_get_task_data(task_id)
        return jsonify({"code": 0, "msg": "ok","data":return_data})
    
    def _get_task_data(task_id):
        file = PROCESS_INFO + f'/{task_id}.json'
        if not Path(file).is_file():
            if task_id in config.uuid_logs_queue:
                return {"code": -1, "msg": _get_order(task_id)}

            return {"code": 1, "msg": f"The task {task_id} does not exist"}

        try:
            data = json.loads(Path(file).read_text(encoding='utf-8'))
        except Exception as e:
            return {"code": -1, "msg": Path(file).read_text(encoding='utf-8')}

        if data['type'] == 'error':
            return {"code": 3, "msg": data["text"]}
        if data['type'] in logs_status_list:
            text=data.get('text','').strip()
            return {"code": -1, "msg": text if text else 'Waiting for processing'}
        # 完成，输出所有文件
        file_list = _get_files_in_directory(f'{TARGET_DIR}/{task_id}')
        if len(file_list) < 1:
            return {"code": 4, "msg": 'No result files were generated, something may have gone wrong'}

        return {
            "code": 0,
            "msg": "ok",
            "data": {
                # "absolute_path": [f'{TARGET_DIR}/{task_id}/{name}' for name in file_list],
                "url": [f'{request.scheme}://{request.host}/{API_RESOURCE}/{task_id}/{name}' for name in file_list],
            }
        }

    # 排队
    def _get_order(task_id):
        order_num=0
        for it in config.prepare_queue:
            order_num+=1
            if it.uuid == task_id:
                return f"No.{order_num} on perpare queue"
        
        order_num=0
        for it in config.regcon_queue:
            order_num+=1
            if it.uuid == task_id:
                return f"No.{order_num} on perpare queue"
        order_num=0
        for it in config.trans_queue:
            order_num+=1
            if it.uuid == task_id:
                return f"No.{order_num} on perpare queue"
        order_num=0
        for it in config.dubb_queue:
            order_num+=1
            if it.uuid == task_id:
                return f"No.{order_num} on perpare queue"
        order_num=0
        for it in config.align_queue:
            order_num+=1
            if it.uuid == task_id:
                return f"No.{order_num} on perpare queue"
        order_num=0
        for it in config.assemb_queue:
            order_num+=1
            if it.uuid == task_id:
                return f"No.{order_num} on perpare queue"
        return f"Waiting in queue"
    
    def _get_files_in_directory(dirname):
        """
        使用 pathlib 库获取指定目录下的所有文件名，并返回一个文件名列表。

        参数:
        dirname (str): 要获取文件的目录路径

        返回:
        list: 包含目录中所有文件名的列表
        """
        try:
            # 使用 Path 对象获取目录中的所有文件
            path = Path(dirname)
            files = [f.name for f in path.iterdir() if f.is_file()]
            return files
        except Exception as e:
            print(f"Error while accessing directory {dirname}: {e}")
            return []

    # ApplySubtitleStyleTask class for processing subtitle styling and dubbing
    class ApplySubtitleStyleTask(BaseTask):
        def __init__(self, cfg):
            super().__init__(cfg)
            # This is a standalone task that doesn't need the full pipeline
            self.shoud_recogn = False
            self.shoud_trans = False
            self.shoud_dubbing = False
            self.shoud_hebing = False

        def prepare(self):
            """Main execution method"""
            try:
                self._signal(text="Starting subtitle style application task")

                # Step 1: Setup local files (no download needed)
                self._setup_files()
                if self._exit():
                    return

                # Step 2: Apply subtitle styling
                self._apply_subtitle_styling()
                if self._exit():
                    return

                # Step 3: Generate dubbing if enabled
                if self.cfg.get('dubbing_settings') and self.cfg['dubbing_settings'].get('enable_dubbing'):
                    self._generate_dubbing()
                    if self._exit():
                        return

                # Step 4: Merge video with styled subtitles and dubbing
                self._merge_video()
                if self._exit():
                    return

                self._signal(text="Task completed successfully", type="succeed")
                self.hasend = True

            except Exception as e:
                self._signal(text=f"Error: {str(e)}", type="error")
                self.hasend = True
                raise

        def assembling(self):
            """Required by BaseTask - called after prepare"""
            pass

        def task_done(self):
            """Required by BaseTask - cleanup and finalization"""
            pass

        def _setup_files(self):
            """Setup local file paths"""
            self._signal(text="Setting up files...")
            self.precent = 10

            try:
                # Use local file paths directly
                self.srt_file = self.cfg['srt_path']
                self.video_file = self.cfg['video_path']

                # Verify files exist
                if not Path(self.srt_file).exists():
                    raise Exception(f"SRT file not found: {self.srt_file}")
                if not Path(self.video_file).exists():
                    raise Exception(f"Video file not found: {self.video_file}")

                self._signal(text="Files verified")
                self.precent = 20

            except Exception as e:
                raise Exception(f"Failed to setup files: {str(e)}")

        def _apply_subtitle_styling(self):
            """Apply styling to subtitles"""
            self._signal(text="Applying subtitle styling...")
            self.precent = 40

            try:
                subtitle_style = self.cfg['subtitle_style']
                subtitle_layout = self.cfg['subtitle_layout']

                # Create ASS file with custom styling
                self.styled_subtitle_file = f"{self.cfg['cache_folder']}/styled_subtitle.ass"

                if subtitle_layout == 'double':
                    # Create double subtitle layout (primary + secondary)
                    self._create_double_subtitle_ass()
                else:
                    # Create single subtitle layout (primary only)
                    self._create_single_subtitle_ass()

                self._signal(text="Subtitle styling applied")
                self.precent = 50

            except Exception as e:
                raise Exception(f"Failed to apply subtitle styling: {str(e)}")

        def _create_single_subtitle_ass(self):
            """Create ASS file with single subtitle styling"""
            try:
                primary_style = self.cfg['subtitle_style']['primary']

                # Convert SRT to basic ASS first
                maxlen = 40
                tools.srt2ass(self.srt_file, self.styled_subtitle_file, maxlen)

                # Read and modify ASS content
                with open(self.styled_subtitle_file, 'r', encoding='utf-8') as f:
                    ass_content = f.readlines()

                # Find and update the Style line
                for i, line in enumerate(ass_content):
                    if line.startswith('Style: Default,'):
                        # Create new style line with primary settings
                        style_parts = line.strip().split(',')
                        if len(style_parts) >= 23:
                            style_parts[1] = primary_style['font_family']  # Fontname
                            style_parts[2] = str(primary_style['font_size'])  # Fontsize
                            style_parts[3] = primary_style['color']  # PrimaryColour
                            style_parts[5] = primary_style['stroke_color'] if primary_style['show_stroke'] else primary_style['color']  # OutlineColour
                            style_parts[6] = primary_style['background_color'] if primary_style['show_background'] else '&H0'  # BackColour
                            style_parts[16] = str(primary_style['stroke_width']) if primary_style['show_stroke'] else '0'  # Outline
                            style_parts[17] = '1' if primary_style['show_shadow'] else '0'  # Shadow
                            style_parts[19] = '2'  # Alignment (bottom)
                            style_parts[22] = str(primary_style['margin_v'])  # MarginV

                            ass_content[i] = ','.join(style_parts) + '\n'
                            break

                # Write back the modified content
                with open(self.styled_subtitle_file, 'w', encoding='utf-8') as f:
                    f.writelines(ass_content)

            except Exception as e:
                config.logger.error(f"Error creating single subtitle ASS: {str(e)}")
                raise

        def _create_double_subtitle_ass(self):
            """Create ASS file with double subtitle styling (primary + secondary)"""
            try:
                primary_style = self.cfg['subtitle_style']['primary']
                secondary_style = self.cfg['subtitle_style']['secondary']

                # Read SRT content
                srt_subtitles = tools.get_subtitle_from_srt(self.srt_file)

                # Create ASS header
                ass_content = [
                    "[Script Info]\n",
                    "Title: Double Subtitle\n",
                    "ScriptType: v4.00+\n",
                    "PlayResX: 1920\n",
                    "PlayResY: 1080\n",
                    "ScaledBorderAndShadow: yes\n",
                    "\n",
                    "[V4+ Styles]\n",
                    "Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n"
                ]

                # Add primary style
                primary_outline = str(primary_style['stroke_width']) if primary_style['show_stroke'] else '0'
                primary_shadow = '1' if primary_style['show_shadow'] else '0'
                primary_back_color = primary_style['background_color'] if primary_style['show_background'] else '&H0'

                primary_style_line = f"Style: Primary,{primary_style['font_family']},{primary_style['font_size']},{primary_style['color']},{primary_style['color']},{primary_style['stroke_color']},{primary_back_color},0,0,0,0,100,100,0,0,1,{primary_outline},{primary_shadow},2,10,10,{primary_style['margin_v']},1\n"
                ass_content.append(primary_style_line)

                # Add secondary style
                secondary_outline = str(secondary_style['stroke_width']) if secondary_style['show_stroke'] else '0'
                secondary_shadow = '1' if secondary_style['show_shadow'] else '0'
                secondary_back_color = secondary_style['background_color'] if secondary_style['show_background'] else '&H0'

                secondary_style_line = f"Style: Secondary,{secondary_style['font_family']},{secondary_style['font_size']},{secondary_style['color']},{secondary_style['color']},{secondary_style['stroke_color']},{secondary_back_color},0,0,0,0,100,100,0,0,1,{secondary_outline},{secondary_shadow},2,10,10,{secondary_style['margin_v']},1\n"
                ass_content.append(secondary_style_line)

                # Add events header
                ass_content.extend([
                    "\n",
                    "[Events]\n",
                    "Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n"
                ])

                # Add dialogue lines (assuming we want to show both primary and secondary for each subtitle)
                for subtitle in srt_subtitles:
                    start_time = subtitle['startraw'].replace(',', '.')
                    end_time = subtitle['endraw'].replace(',', '.')
                    text = subtitle['text'].replace('\n', '\\N')

                    # Add primary subtitle
                    primary_line = f"Dialogue: 0,{start_time},{end_time},Primary,,0,0,0,,{text}\n"
                    ass_content.append(primary_line)

                    # Add secondary subtitle (could be translation or same text with different styling)
                    secondary_line = f"Dialogue: 0,{start_time},{end_time},Secondary,,0,0,0,,{text}\n"
                    ass_content.append(secondary_line)

                # Write ASS file
                with open(self.styled_subtitle_file, 'w', encoding='utf-8') as f:
                    f.writelines(ass_content)

            except Exception as e:
                config.logger.error(f"Error creating double subtitle ASS: {str(e)}")
                raise

        def _generate_dubbing(self):
            """Generate dubbing audio from subtitles"""
            self._signal(text="Generating dubbing audio...")
            self.precent = 60

            try:
                dubbing_settings = self.cfg['dubbing_settings']

                # Create dubbing configuration
                dubbing_cfg = {
                    'target_sub': self.srt_file,
                    'target_wav': f"{self.cfg['cache_folder']}/dubbing_audio.wav",
                    'tts_type': dubbing_settings.get('tts_type', 0),
                    'voice_role': dubbing_settings.get('voice_role', ''),
                    'voice_rate': dubbing_settings.get('voice_rate', '+0%'),
                    'volume': dubbing_settings.get('volume', '+0%'),
                    'pitch': dubbing_settings.get('pitch', '+0Hz'),
                    'target_language_code': dubbing_settings.get('target_language', 'zh-cn'),
                    'cache_folder': self.cfg['cache_folder'],
                    'uuid': self.uuid,
                    'basename': 'subtitle_dubbing',
                    'noextname': 'subtitle_dubbing'
                }

                # Use existing dubbing functionality
                dubbing_task = DubbingSrt(dubbing_cfg)
                dubbing_task.dubbing()

                self.dubbing_audio_file = dubbing_cfg['target_wav']
                self._signal(text="Dubbing audio generated")
                self.precent = 70

            except Exception as e:
                raise Exception(f"Failed to generate dubbing: {str(e)}")

        def _merge_video(self):
            """Merge video with styled subtitles and dubbing"""
            self._signal(text="Merging video with subtitles and audio...")
            self.precent = 80

            try:
                output_file = f"{self.cfg['target_dir']}/output_video.mp4"
                subtitle_settings = self.cfg['subtitle_settings']

                # Build ffmpeg command
                cmd = ['-y', '-i', self.video_file]

                # Add dubbing audio if available
                if hasattr(self, 'dubbing_audio_file') and Path(self.dubbing_audio_file).exists():
                    cmd.extend(['-i', self.dubbing_audio_file])

                # Set video codec
                cmd.extend(['-c:v', 'libx264'])

                # Handle subtitles
                if subtitle_settings.get('subtitle_type', 1) == 1:  # Hard subtitles
                    # Embed hard subtitles using ASS file
                    cmd.extend(['-vf', f"subtitles={Path(self.styled_subtitle_file).name}"])
                    # Change to subtitle directory for relative path
                    os.chdir(Path(self.styled_subtitle_file).parent)
                else:  # Soft subtitles
                    # Add soft subtitles
                    cmd.extend(['-i', self.styled_subtitle_file])
                    cmd.extend(['-c:s', 'mov_text'])
                    cmd.extend(['-map', '0:v:0'])  # Video from first input
                    if hasattr(self, 'dubbing_audio_file'):
                        cmd.extend(['-map', '1:a:0'])  # Audio from second input (dubbing)
                        cmd.extend(['-map', '2:s:0'])  # Subtitles from third input
                    else:
                        cmd.extend(['-map', '1:s:0'])  # Subtitles from second input

                # Audio settings
                if hasattr(self, 'dubbing_audio_file') and Path(self.dubbing_audio_file).exists():
                    cmd.extend(['-c:a', 'aac', '-b:a', '192k'])
                else:
                    # Copy original audio if no dubbing
                    cmd.extend(['-c:a', 'copy'])

                # Quality settings
                cmd.extend(['-crf', str(config.settings.get('crf', 25))])
                cmd.extend(['-preset', config.settings.get('preset', 'medium')])
                cmd.extend(['-movflags', '+faststart'])

                # Output file
                cmd.append(output_file)

                # Execute ffmpeg command
                tools.runffmpeg(cmd)

                # Change back to original directory
                os.chdir(config.ROOT_DIR)

                self._signal(text="Video processing completed")
                self.precent = 95

                # Copy result to target directory
                if Path(output_file).exists():
                    self._signal(text="Output video created successfully")
                    self.precent = 100
                else:
                    raise Exception("Output video file was not created")

            except Exception as e:
                os.chdir(config.ROOT_DIR)  # Ensure we're back in the right directory
                raise Exception(f"Failed to merge video: {str(e)}")

    def get_local_file_path(request, name):
        # convert http://127.0.0.1:9011/file/250601/2155d0fba834bb41efe7ed00fc6fe309.mp4 to /250601/2155d0fba834bb41efe7ed00fc6fe309.mp4
        if name.startswith(f'{request.scheme}://{request.host}/file/'):
            name = name.replace(f'{request.scheme}://{request.host}/file/', '')
            name = '/tmp/upload/' + name
        return name
    def _listen_queue():
        # 监听队列日志 uuid_logs_queue 不在停止中的 stoped_uuid_set
        Path(TARGET_DIR + f'/processinfo').mkdir(parents=True, exist_ok=True)
        while 1:
            # 找出未停止的
            uuid_list = list(config.uuid_logs_queue.keys())
            uuid_list = [uuid for uuid in uuid_list if uuid not in config.stoped_uuid_set]
            # 全部结束
            if len(uuid_list) < 1:
                time.sleep(1)
                continue
            while len(uuid_list) > 0:
                uuid = uuid_list.pop(0)
                if uuid in config.stoped_uuid_set:
                    continue
                try:
                    q = config.uuid_logs_queue.get(uuid)
                    if not q:
                        continue
                    data = q.get(block=False)
                    if not data:
                        continue

                    if data['type'] not in end_status_list + logs_status_list:
                        continue
                    with open(PROCESS_INFO + f'/{uuid}.json', 'w', encoding='utf-8') as f:
                        f.write(json.dumps(data))
                    if data['type'] in end_status_list:
                        config.stoped_uuid_set.add(uuid)
                        del config.uuid_logs_queue[uuid]
                except Exception:
                    pass
            time.sleep(0.1)

    multiprocessing.freeze_support()  # Windows 上需要这个来避免子进程的递归执行问题
    print(f'Starting... API URL is   http://{HOST}:{PORT}')
    print(f'Document at https://pyvideotrans.com/api-cn')
    start_thread()
    threading.Thread(target=_listen_queue).start()
    try:
        print(f'\nAPI URL is   http://{HOST}:{PORT}')
        serve(app, host=HOST, port=int(PORT))
    except Exception as e:
        import traceback
        traceback.print_exc()
